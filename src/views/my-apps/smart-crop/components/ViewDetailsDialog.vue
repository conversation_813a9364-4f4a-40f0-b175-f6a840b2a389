<template>
  <el-dialog 
    v-model="dialogVisible" 
    title="裁图任务详情" 
    width="900px" 
    align-center>
    <div v-if="task" class="space-y-6">
      <!-- 任务基本信息 -->
      <div class="bg-gray-50 dark:bg-dark-card rounded-lg p-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">任务信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-600 dark:text-dark-text-secondary">任务ID</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-dark-text font-mono">{{ task.id }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-600 dark:text-dark-text-secondary">创建时间</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-dark-text">{{ task.createTime }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-600 dark:text-dark-text-secondary">操作人</label>
            <p class="mt-1 text-sm text-gray-900 dark:text-dark-text">{{ task.operator }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-600 dark:text-dark-text-secondary">任务状态</label>
            <span :class="getStatusClass(task.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1">
              {{ getStatusText(task.status) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 处理统计 -->
      <div class="grid grid-cols-3 gap-4">
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ task.targetCount }}</div>
          <div class="text-sm text-blue-600 dark:text-blue-400">目标数量</div>
        </div>
        <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ task.successCount }}</div>
          <div class="text-sm text-green-600 dark:text-green-400">成功数量</div>
        </div>
        <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-red-600 dark:text-red-400">{{ task.targetCount - task.successCount }}</div>
          <div class="text-sm text-red-600 dark:text-red-400">失败数量</div>
        </div>
      </div>

      <!-- 处理进度 -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">处理进度</h3>
        <div class="space-y-3">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600 dark:text-dark-text-secondary">总体进度</span>
            <span class="text-gray-900 dark:text-dark-text">{{ Math.round((task.successCount / task.targetCount) * 100) }}%</span>
          </div>
          <el-progress 
            :percentage="Math.round((task.successCount / task.targetCount) * 100)"
            :status="task.status === 'completed' ? 'success' : task.status === 'failed' ? 'exception' : undefined"
            :stroke-width="8" />
        </div>
      </div>

      <!-- 处理结果列表 -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">处理结果</h3>
        <div class="border border-gray-200 dark:border-dark-border rounded-lg overflow-hidden">
          <el-table :data="processResults" style="width: 100%" max-height="300" :table-layout="'fixed'">
            <el-table-column prop="fileName" label="文件名" width="200">
              <template #default="scope">
                <div class="flex items-center space-x-2">
                  <img v-if="scope.row.thumbnail" :src="scope.row.thumbnail" 
                       class="w-8 h-8 object-cover rounded" />
                  <span class="text-sm">{{ scope.row.fileName }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="originalSize" label="原始尺寸" width="120">
              <template #default="scope">
                <span class="text-sm text-gray-600 dark:text-dark-text-secondary">
                  {{ scope.row.originalSize }}
                </span>
              </template>
            </el-table-column>
            
            <el-table-column prop="cropSize" label="裁图尺寸" width="120">
              <template #default="scope">
                <span class="text-sm text-gray-600 dark:text-dark-text-secondary">
                  {{ scope.row.cropSize }}
                </span>
              </template>
            </el-table-column>
            
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <span :class="getItemStatusClass(scope.row.status)"
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium">
                  {{ getItemStatusText(scope.row.status) }}
                </span>
              </template>
            </el-table-column>

            <el-table-column label="预览" width="80">
              <template #default="scope">
                <button v-if="scope.row.status === 'success'"
                        @click="previewResult(scope.row)"
                        class="inline-flex items-center justify-center w-8 h-8 text-green-600 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-all duration-200">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                </button>
                <span v-else class="text-gray-400 text-sm">-</span>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="80">
              <template #default="scope">
                <button v-if="scope.row.status === 'success'"
                        @click="downloadResult(scope.row)"
                        class="inline-flex items-center justify-center w-8 h-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </button>
                <span v-else class="text-gray-400 text-sm">-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="task.status === 'failed' && task.errorMessage">
        <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">错误信息</h3>
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p class="text-sm text-red-700 dark:text-red-400">{{ task.errorMessage }}</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <div class="flex space-x-2">
          <el-button v-if="task?.status === 'completed'" @click="downloadAll" type="primary">
            下载全部结果
          </el-button>
          <el-button v-if="task?.status === 'failed'" @click="retryTask" type="warning">
            重试任务
          </el-button>
        </div>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 图片预览对话框 -->
  <el-dialog v-model="showPreview" title="图片预览" width="600px" align-center>
    <div v-if="previewImage" class="text-center">
      <img :src="previewImage.url" :alt="previewImage.fileName" class="max-w-full max-h-96 mx-auto" />
      <div class="mt-4 text-sm text-gray-600 dark:text-dark-text-secondary">
        {{ previewImage.fileName }} - {{ previewImage.cropSize }}
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';

// Props
const props = defineProps<{
  modelValue: boolean;
  task: any;
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

// 类型定义
interface ProcessResult {
  fileName: string;
  originalSize: string;
  cropSize: string;
  status: 'success' | 'failed';
  thumbnail: string;
  downloadUrl?: string;
  errorMessage?: string;
  url?: string;
}

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const showPreview = ref(false);
const previewImage = ref<ProcessResult | null>(null);

// 模拟处理结果数据
const processResults = ref([
  {
    fileName: 'image1.jpg',
    originalSize: '1920x1080',
    cropSize: '800x800',
    status: 'success',
    thumbnail: 'https://via.placeholder.com/32x32',
    downloadUrl: '#'
  },
  {
    fileName: 'image2.jpg',
    originalSize: '1600x900',
    cropSize: '800x800',
    status: 'success',
    thumbnail: 'https://via.placeholder.com/32x32',
    downloadUrl: '#'
  },
  {
    fileName: 'image3.jpg',
    originalSize: '1280x720',
    cropSize: '800x800',
    status: 'failed',
    thumbnail: 'https://via.placeholder.com/32x32',
    errorMessage: '图片格式不支持'
  }
]);

// 状态相关方法
const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    'completed': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
    'processing': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
    'failed': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
    'pending': 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
  };
  return statusClasses[status] || statusClasses['pending'];
};

const getStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'completed': '已完成',
    'processing': '处理中',
    'failed': '失败',
    'pending': '等待中'
  };
  return statusTexts[status] || '未知';
};

const getItemStatusClass = (status: ProcessResult['status']) => {
  const statusClasses: Record<ProcessResult['status'], string> = {
    'success': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
    'failed': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
  };
  return statusClasses[status] || '';
};

const getItemStatusText = (status: ProcessResult['status']) => {
  const statusTexts: Record<ProcessResult['status'], string> = {
    'success': '成功',
    'failed': '失败'
  };
  return statusTexts[status] || '未知';
};

// 操作方法
const downloadResult = (item: any) => {
  ElMessage.success(`正在下载 ${item.fileName}`);
  // 这里应该实现实际的下载逻辑
};

const previewResult = (item: any) => {
  previewImage.value = item;
  showPreview.value = true;
};

const downloadAll = () => {
  ElMessage.success('正在打包下载所有结果...');
  // 这里应该实现批量下载逻辑
};

const retryTask = () => {
  ElMessage.success('正在重试任务...');
  // 这里应该实现重试逻辑
};
</script>
