<template>
  <el-dialog 
    v-model="dialogVisible" 
    title="新建裁图任务" 
    width="800px" 
    align-center
    @close="resetForm">
    <div class="space-y-6">
      <!-- 图片上传区域 -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">选择图片</h3>
        <div class="flex space-x-4">
          <!-- 上传图片按钮 -->
          <div class="flex-1">
            <el-upload
              ref="uploadRef"
              :file-list="fileList"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :auto-upload="false"
              multiple
              accept="image/*"
              drag
              class="w-full">
              <div class="text-center py-8">
                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <p class="text-gray-600 dark:text-dark-text-secondary">点击或拖拽图片到此处上传</p>
                <p class="text-sm text-gray-500 dark:text-dark-text-secondary mt-1">支持 JPG、PNG、GIF 格式</p>
              </div>
            </el-upload>
          </div>

          <!-- 图库选择按钮 -->
          <div class="flex-shrink-0">
            <button @click="showGalleryDialog = true"
              class="h-full px-6 py-4 border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg hover:border-green-400 dark:hover:border-green-500 transition-colors duration-200 flex flex-col items-center justify-center min-h-[120px]">
              <svg class="w-8 h-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              <span class="text-sm text-gray-600 dark:text-dark-text-secondary">从图库选择</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 已选择的图片列表 -->
      <div v-if="selectedImages.length > 0">
        <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">
          已选择图片 ({{ selectedImages.length }})
        </h3>
        <div class="grid grid-cols-4 gap-4 max-h-60 overflow-y-auto">
          <div v-for="(image, index) in selectedImages" :key="index" 
               class="relative group">
            <img :src="image.url" :alt="image.name" 
                 class="w-full h-20 object-cover rounded-lg border border-gray-200 dark:border-dark-border">
            <button @click="removeImage(index)"
                    class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 裁图设置 -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">裁图设置</h3>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2">
              输出尺寸
            </label>
            <el-select v-model="cropSettings.size" placeholder="选择输出尺寸" class="w-full">
              <el-option label="1:1 正方形" value="1:1" />
              <el-option label="16:9 横屏" value="16:9" />
              <el-option label="9:16 竖屏" value="9:16" />
              <el-option label="4:3 标准" value="4:3" />
              <el-option label="自定义" value="custom" />
            </el-select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2">
              输出质量
            </label>
            <el-select v-model="cropSettings.quality" placeholder="选择输出质量" class="w-full">
              <el-option label="高质量" value="high" />
              <el-option label="中等质量" value="medium" />
              <el-option label="压缩质量" value="low" />
            </el-select>
          </div>
        </div>

        <!-- 自定义尺寸 -->
        <div v-if="cropSettings.size === 'custom'" class="mt-4 grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2">
              宽度 (px)
            </label>
            <el-input v-model="cropSettings.customWidth" type="number" placeholder="输入宽度" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2">
              高度 (px)
            </label>
            <el-input v-model="cropSettings.customHeight" type="number" placeholder="输入高度" />
          </div>
        </div>
      </div>

      <!-- 任务信息 -->
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">任务信息</h3>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2">
            任务名称
          </label>
          <el-input v-model="taskName" placeholder="输入任务名称（可选）" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-500 dark:text-dark-text-secondary">
          将处理 {{ selectedImages.length }} 张图片
        </div>
        <div class="flex space-x-3">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitTask" :disabled="selectedImages.length === 0">
            提交任务
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>

  <!-- 图库选择对话框 -->
  <GallerySelectDialog v-model="showGalleryDialog" @select="handleGallerySelect" />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import GallerySelectDialog from './GallerySelectDialog.vue';

// Props
const props = defineProps<{
  modelValue: boolean;
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'success': [];
}>();

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const showGalleryDialog = ref(false);
const fileList = ref([]);
const selectedImages = ref([]);
const taskName = ref('');

const cropSettings = ref({
  size: '1:1',
  quality: 'high',
  customWidth: '',
  customHeight: ''
});

// 文件处理方法
const handleFileChange = (file: any) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    selectedImages.value.push({
      name: file.name,
      url: e.target?.result as string,
      file: file.raw
    });
  };
  reader.readAsDataURL(file.raw);
};

const handleFileRemove = (file: any) => {
  const index = selectedImages.value.findIndex(img => img.name === file.name);
  if (index > -1) {
    selectedImages.value.splice(index, 1);
  }
};

const removeImage = (index: number) => {
  selectedImages.value.splice(index, 1);
  // 同时从文件列表中移除
  fileList.value.splice(index, 1);
};

// 图库选择处理
const handleGallerySelect = (images: any[]) => {
  selectedImages.value.push(...images);
  showGalleryDialog.value = false;
};

// 提交任务
const submitTask = () => {
  if (selectedImages.value.length === 0) {
    ElMessage.warning('请先选择要裁图的图片');
    return;
  }

  // 这里应该调用API提交裁图任务
  ElMessage.success(`已提交裁图任务，将处理 ${selectedImages.value.length} 张图片`);
  
  emit('success');
  dialogVisible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  selectedImages.value = [];
  fileList.value = [];
  taskName.value = '';
  cropSettings.value = {
    size: '1:1',
    quality: 'high',
    customWidth: '',
    customHeight: ''
  };
};
</script>
